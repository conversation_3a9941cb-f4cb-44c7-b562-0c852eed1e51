#!/usr/bin/env python3
"""
Run Comprehensive CLEAR-E Experiments for Paper Results
"""

import os
import subprocess
import json
import time
from datetime import datetime

def run_experiment(dataset, model, pred_len, method, epochs=5, itr=1):
    """Run a single experiment and return results"""
    
    print(f"Running: {dataset} - {model} - {pred_len}h - {method}")
    start_time = time.time()
    
    # Base command
    cmd = [
        "python", "-u", "run.py",
        "--dataset", dataset,
        "--model", model,
        "--seq_len", "96",
        "--pred_len", str(pred_len),
        "--batch_size", "16",
        "--learning_rate", "0.001",
        "--train_epochs", str(epochs),
        "--itr", str(itr),
        "--features", "M"
    ]
    
    # Add method-specific parameters
    if method == "ClearE":
        cmd.extend([
            "--online_learning_rate", "0.0001",
            "--online_method", "ClearE",
            "--concept_dim", "64",
            "--bottleneck_dim", "32",
            "--metadata_dim", "10",
            "--metadata_hidden_dim", "32",
            "--drift_memory_size", "10",
            "--drift_reg_weight", "0.1",
            "--use_energy_loss",
            "--high_load_threshold", "0.8",
            "--underestimate_penalty", "2.0",
            "--border_type", "online",
            "--pretrain",
            "--save_opt",
            "--only_test",
            "--val_online_lr",
            "--diff_online_lr",
            "--tune_mode", "down_up"
        ])
    elif method == "Online":
        cmd.extend([
            "--online_learning_rate", "0.0001",
            "--online_method", "Online",
            "--border_type", "online",
            "--pretrain",
            "--save_opt",
            "--only_test"
        ])
    elif method == "Proceed":
        cmd.extend([
            "--online_learning_rate", "0.0001",
            "--online_method", "Proceed",
            "--concept_dim", "64",
            "--bottleneck_dim", "32",
            "--border_type", "online",
            "--pretrain",
            "--save_opt",
            "--only_test",
            "--val_online_lr",
            "--diff_online_lr",
            "--tune_mode", "down_up"
        ])
    else:
        # Offline baseline - just train and test
        pass
    
    # Run the experiment
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 min timeout
        
        if result.returncode == 0:
            # Extract results from output
            output = result.stdout
            lines = output.split('\n')
            
            for line in lines:
                if line.startswith('mse:') and 'mae:' in line:
                    parts = line.split(',')
                    mse = float(parts[0].split(':')[1].strip())
                    mae = float(parts[1].split(':')[1].strip())
                    
                    duration = time.time() - start_time
                    return {
                        'dataset': dataset,
                        'model': model,
                        'pred_len': pred_len,
                        'method': method,
                        'mse': mse,
                        'mae': mae,
                        'duration': duration,
                        'success': True,
                        'output': output
                    }
            
            # If no results found in output
            return {
                'dataset': dataset,
                'model': model,
                'pred_len': pred_len,
                'method': method,
                'success': False,
                'error': 'No results found in output',
                'output': output,
                'duration': time.time() - start_time
            }
        else:
            return {
                'dataset': dataset,
                'model': model,
                'pred_len': pred_len,
                'method': method,
                'success': False,
                'error': result.stderr,
                'output': result.stdout,
                'duration': time.time() - start_time
            }
            
    except subprocess.TimeoutExpired:
        return {
            'dataset': dataset,
            'model': model,
            'pred_len': pred_len,
            'method': method,
            'success': False,
            'error': 'Timeout',
            'duration': time.time() - start_time
        }
    except Exception as e:
        return {
            'dataset': dataset,
            'model': model,
            'pred_len': pred_len,
            'method': method,
            'success': False,
            'error': str(e),
            'duration': time.time() - start_time
        }

def ensure_model_trained(dataset, model, epochs=5):
    """Ensure the base model is trained before running online experiments"""
    
    checkpoint_path = f"./checkpoints/{dataset}_96_24_{model}_online_ftM_sl96_ll48_pl24_lr0.001_dm512_nh8_el2_dl1_df2048_fc3_ebtimeF_dtTrue_test_0/checkpoint.pth"
    
    if os.path.exists(checkpoint_path):
        print(f"✓ Model {model} already trained for {dataset}")
        return True
    
    print(f"Training {model} for {dataset}...")
    
    cmd = [
        "python", "-u", "run.py",
        "--dataset", dataset,
        "--model", model,
        "--seq_len", "96",
        "--pred_len", "24",
        "--batch_size", "16",
        "--learning_rate", "0.001",
        "--train_epochs", str(epochs),
        "--itr", "1",
        "--features", "M"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)
        if result.returncode == 0:
            print(f"✓ Successfully trained {model} for {dataset}")
            return True
        else:
            print(f"✗ Failed to train {model} for {dataset}: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Error training {model} for {dataset}: {e}")
        return False

def main():
    # Experimental configuration
    datasets = ["ETTh1", "ETTh2", "ETTm1", "ETTm2", "ECL"]
    models = ["DLinear", "PatchTST"]
    pred_lens = [24, 48, 96]
    methods = ["Offline", "Online", "Proceed", "ClearE"]
    
    print("=== Comprehensive CLEAR-E Experiments ===")
    print(f"Datasets: {datasets}")
    print(f"Models: {models}")
    print(f"Prediction horizons: {pred_lens}")
    print(f"Methods: {methods}")
    print("==========================================")
    
    # Create results directory
    os.makedirs("results/comprehensive_experiments", exist_ok=True)
    
    all_results = []
    total_experiments = len(datasets) * len(models) * len(pred_lens) * len(methods)
    current_experiment = 0
    
    for dataset in datasets:
        for model in models:
            # Ensure base model is trained
            if not ensure_model_trained(dataset, model):
                print(f"Skipping {dataset}-{model} due to training failure")
                continue
                
            for pred_len in pred_lens:
                for method in methods:
                    current_experiment += 1
                    print(f"\n=== Experiment {current_experiment}/{total_experiments} ===")
                    
                    result = run_experiment(dataset, model, pred_len, method)
                    all_results.append(result)
                    
                    if result['success']:
                        print(f"✓ Success: MSE={result['mse']:.4f}, MAE={result['mae']:.4f}")
                    else:
                        print(f"✗ Failed: {result.get('error', 'Unknown error')}")
                    
                    print(f"Duration: {result['duration']:.1f}s")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"results/comprehensive_experiments/results_{timestamp}.json"
    
    summary = {
        'timestamp': timestamp,
        'total_experiments': total_experiments,
        'successful_experiments': sum(1 for r in all_results if r['success']),
        'results': all_results
    }
    
    with open(results_file, 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"\n=== Experiment Summary ===")
    print(f"Total experiments: {total_experiments}")
    print(f"Successful: {summary['successful_experiments']}")
    print(f"Failed: {total_experiments - summary['successful_experiments']}")
    print(f"Success rate: {summary['successful_experiments']/total_experiments*100:.1f}%")
    print(f"Results saved to: {results_file}")
    
    # Create summary table
    create_summary_table(all_results, timestamp)

def create_summary_table(results, timestamp):
    """Create a summary table of results"""
    
    # Group results by dataset and method
    summary_data = {}
    
    for result in results:
        if not result['success']:
            continue
            
        key = f"{result['dataset']}_{result['method']}"
        if key not in summary_data:
            summary_data[key] = {
                'dataset': result['dataset'],
                'method': result['method'],
                'mse_values': [],
                'mae_values': []
            }
        
        summary_data[key]['mse_values'].append(result['mse'])
        summary_data[key]['mae_values'].append(result['mae'])
    
    # Calculate statistics
    import numpy as np
    
    table_data = []
    for key, data in summary_data.items():
        if data['mse_values']:
            mse_mean = np.mean(data['mse_values'])
            mse_std = np.std(data['mse_values'])
            mae_mean = np.mean(data['mae_values'])
            mae_std = np.std(data['mae_values'])
            
            table_data.append({
                'dataset': data['dataset'],
                'method': data['method'],
                'mse_mean': mse_mean,
                'mse_std': mse_std,
                'mae_mean': mae_mean,
                'mae_std': mae_std,
                'n_results': len(data['mse_values'])
            })
    
    # Save table
    table_file = f"results/comprehensive_experiments/summary_table_{timestamp}.json"
    with open(table_file, 'w') as f:
        json.dump(table_data, f, indent=2)
    
    print(f"Summary table saved to: {table_file}")
    
    # Print table
    print("\n=== Results Summary Table ===")
    print(f"{'Dataset':<15} {'Method':<10} {'MSE':<15} {'MAE':<15} {'N':<5}")
    print("-" * 65)
    
    for row in sorted(table_data, key=lambda x: (x['dataset'], x['method'])):
        mse_str = f"{row['mse_mean']:.4f}±{row['mse_std']:.4f}"
        mae_str = f"{row['mae_mean']:.4f}±{row['mae_std']:.4f}"
        print(f"{row['dataset']:<15} {row['method']:<10} {mse_str:<15} {mae_str:<15} {row['n_results']:<5}")

if __name__ == "__main__":
    main()
